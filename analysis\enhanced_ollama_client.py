"""
Enhanced Ollama Client with Context-Aware Analysis
Extends the base OllamaClient with hierarchical context support
"""

import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from analysis.ollama_client import OllamaClient
from analysis.context_framework import (
    AnalysisContext, AnalysisResult, AnalysisInsight,
    ContextLevel, ContextualPromptBuilder
)


class InsightExtractor:
    """Extracts structured insights from analysis text"""

    def extract_insights(
        self,
        analysis: str,
        context: AnalysisContext
    ) -> List[AnalysisInsight]:
        """Extract insights from analysis text"""
        insights = []

        # Extract key points (numbered lists, bullet points)
        key_points = self._extract_key_points(analysis)
        insights.extend(key_points)

        # Extract relationships
        relationships = self._extract_relationships(analysis, context)
        insights.extend(relationships)

        # Extract implications
        implications = self._extract_implications(analysis)
        insights.extend(implications)

        # Extract questions
        questions = self._extract_questions(analysis)
        insights.extend(questions)

        return insights

    def _extract_key_points(self, analysis: str) -> List[AnalysisInsight]:
        """Extract key points from numbered lists and bullet points"""
        insights = []

        # Numbered lists
        numbered_points = re.findall(r'\d+\.\s*([^\n]+)', analysis)
        for point in numbered_points:
            insights.append(AnalysisInsight(
                type="key_point",
                content=point.strip(),
                confidence=0.8
            ))

        # Bullet points
        bullet_points = re.findall(r'[-*]\s*([^\n]+)', analysis)
        for point in bullet_points:
            insights.append(AnalysisInsight(
                type="key_point",
                content=point.strip(),
                confidence=0.7
            ))

        return insights[:5]  # Limit to top 5

    def _extract_relationships(
        self,
        analysis: str,
        context: AnalysisContext
    ) -> List[AnalysisInsight]:
        """Extract relationship statements"""
        insights = []

        # Look for relationship keywords
        relationship_patterns = [
            r'([^.]+(?:relates to|connected to|linked with|associated with)[^.]+)',
            r'([^.]+(?:influences|affects|impacts)[^.]+)',
            r'([^.]+(?:depends on|requires|needs)[^.]+)'
        ]

        for pattern in relationship_patterns:
            matches = re.findall(pattern, analysis, re.IGNORECASE)
            for match in matches:
                insights.append(AnalysisInsight(
                    type="relationship",
                    content=match.strip(),
                    confidence=0.6
                ))

        return insights[:3]  # Limit to top 3

    def _extract_implications(self, analysis: str) -> List[AnalysisInsight]:
        """Extract implication statements"""
        insights = []

        # Look for implication keywords
        implication_patterns = [
            r'([^.]+(?:implies|suggests|indicates)[^.]+)',
            r'([^.]+(?:therefore|thus|consequently)[^.]+)',
            r'([^.]+(?:this means|this suggests)[^.]+)'
        ]

        for pattern in implication_patterns:
            matches = re.findall(pattern, analysis, re.IGNORECASE)
            for match in matches:
                insights.append(AnalysisInsight(
                    type="implication",
                    content=match.strip(),
                    confidence=0.7
                ))

        return insights[:3]  # Limit to top 3

    def _extract_questions(self, analysis: str) -> List[AnalysisInsight]:
        """Extract questions from analysis"""
        insights = []

        # Find question sentences
        questions = re.findall(r'([^.!?]*\?)', analysis)
        for question in questions:
            insights.append(AnalysisInsight(
                type="question",
                content=question.strip(),
                confidence=0.5
            ))

        return insights[:2]  # Limit to top 2

class EnhancedOllamaClient(OllamaClient):
    """Enhanced Ollama client with context-aware analysis capabilities"""

    def __init__(self, config):
        super().__init__(config)
        self.prompt_builder = ContextualPromptBuilder()
        self.insight_extractor = InsightExtractor()

    def analyze_with_context(
        self,
        topic: str,
        context: AnalysisContext,
        level: ContextLevel,
        additional_params: Optional[Dict[str, Any]] = None
    ) -> AnalysisResult:
        """Analyze topic with full context awareness"""

        # Build context-aware prompt
        prompt = self.prompt_builder.build_prompt(
            topic, context, level, additional_params if additional_params is not None else {}
        )

        # Generate analysis
        analysis = self.generate(
            prompt=prompt,
            system_prompt=self._get_system_prompt(level)
        )

        if not analysis:
            return self._create_empty_result(topic, level)

        # Extract structured insights
        insights = self.insight_extractor.extract_insights(analysis, context)

        # Identify immediate connections
        connections = self._identify_immediate_connections(topic, context, analysis)

        # Calculate quality score
        quality_score = self._calculate_quality_score(analysis, insights)

        return AnalysisResult(
            topic=topic,
            analysis=analysis,
            insights=insights,
            context_level=level,
            connections=connections,
            quality_score=quality_score,
            metadata=self._extract_metadata(analysis, level)
        )

    def progressive_analysis_with_progress(
        self,
        main_topic: str,
        sub_topics_tree: List[Any],
        config: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Perform progressive analysis with progress reporting"""

        # Flatten tree to get sub_topics list for compatibility
        sub_topics = self._flatten_tree_to_topics(sub_topics_tree)

        # Initialize context
        context = AnalysisContext(
            main_topic=main_topic,
            topic_hierarchy={'sub_topics': sub_topics},
            analysis_goals=config.get('goals', ['comprehensive_analysis'])
        )

        results = {
            "type": "progressive",
            "topic": main_topic,
            "sub_analyses": [],
            "connections": [],
            "synthesis": "",
            "metadata": {
                "model": config.get("model", self.default_model),
                "timestamp": datetime.now().isoformat(),
                "context_aware": True
            }
        }

        try:
            # Phase 1: Main Topic Analysis
            if progress_callback:
                progress_callback("initialization", 0, 1, "Analyzing main topic")

            self.logger.info(f"Starting progressive analysis for: {main_topic}")
            main_analysis = self.analyze_with_context(
                main_topic, context, ContextLevel.ROOT
            )
            context.update_with_analysis(main_analysis)

            # Phase 2: Sub-topic Analysis with progress
            if progress_callback:
                progress_callback("topic_analysis", 0, len(sub_topics), "Starting sub-topic analysis")

            self.logger.info(f"Starting sub-topic analysis for {len(sub_topics)} topics")
            for i, sub_topic in enumerate(sub_topics):
                if progress_callback:
                    progress_callback("topic_analysis", i, len(sub_topics), f"Analyzing: {sub_topic}")

                self.logger.info(f"Analyzing sub-topic {i+1}/{len(sub_topics)}: {sub_topic}")

                analysis = self.analyze_with_context(
                    sub_topic, context, ContextLevel.BRANCH
                )
                context.update_with_analysis(analysis)

                results["sub_analyses"].append({
                    "topic": analysis.topic,
                    "analysis": analysis.analysis,
                    "insights": [self._insight_to_dict(i) for i in analysis.insights],
                    "connections": analysis.connections,
                    "quality_score": analysis.quality_score
                })

            # Phase 3: Enhanced Connection Discovery
            if progress_callback:
                progress_callback("connections", 0, 1, "Discovering enhanced connections")

            self.logger.info("Discovering enhanced connections")
            connections = self._discover_enhanced_connections(context)
            results["connections"] = connections

            # Phase 4: Synthesis Generation
            if progress_callback:
                progress_callback("synthesis", 0, 1, "Generating synthesis")

            self.logger.info("Generating synthesis")
            synthesis = self._generate_synthesis(context)
            results["synthesis"] = synthesis

            # Add context summary
            results["context_summary"] = context.get_context_summary()

            if progress_callback:
                progress_callback("synthesis", 1, 1, "Analysis complete")

            return results

        except Exception as e:
            self.logger.error(f"Error in progressive analysis: {e}")
            return self._create_error_result(main_topic, str(e))

    def _flatten_tree_to_topics(self, tree: List[Any]) -> List[str]:
        """Flatten a tree structure to a list of topic strings"""
        topics = []

        for item in tree:
            if isinstance(item, str):
                topics.append(item)
            elif isinstance(item, dict):
                if "title" in item:
                    topics.append(item["title"])
                elif "name" in item:
                    topics.append(item["name"])

                # Recursively process children
                if "children" in item:
                    topics.extend(self._flatten_tree_to_topics(item["children"]))
                elif "sub_topics" in item:
                    topics.extend(self._flatten_tree_to_topics(item["sub_topics"]))

        return topics

    def progressive_analysis(
        self,
        main_topic: str,
        sub_topics: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform progressive analysis with context building"""

        # Initialize context
        context = AnalysisContext(
            main_topic=main_topic,
            topic_hierarchy={'sub_topics': sub_topics},
            analysis_goals=config.get('goals', ['comprehensive_analysis'])
        )

        results = {
            "type": "progressive",
            "topic": main_topic,
            "sub_analyses": [],
            "connections": [],
            "synthesis": "",
            "metadata": {
                "model": config.get("model", self.default_model),
                "timestamp": datetime.now().isoformat(),
                "context_aware": True
            }
        }

        try:
            # Phase 1: Foundation Analysis
            self.logger.info(f"Starting foundation analysis for: {main_topic}")
            main_analysis = self.analyze_with_context(
                main_topic, context, ContextLevel.ROOT
            )
            context.update_with_analysis(main_analysis)
            results["main_analysis"] = {
                "topic": main_analysis.topic,
                "analysis": main_analysis.analysis,
                "insights": [self._insight_to_dict(i) for i in main_analysis.insights],
                "quality_score": main_analysis.quality_score
            }

            # Phase 2: Sub-topic Analysis
            self.logger.info(f"Starting sub-topic analysis for {len(sub_topics)} topics")
            for i, sub_topic in enumerate(sub_topics):
                self.logger.info(f"Analyzing sub-topic {i+1}/{len(sub_topics)}: {sub_topic}")

                analysis = self.analyze_with_context(
                    sub_topic, context, ContextLevel.BRANCH
                )
                context.update_with_analysis(analysis)

                results["sub_analyses"].append({
                    "topic": analysis.topic,
                    "analysis": analysis.analysis,
                    "insights": [self._insight_to_dict(i) for i in analysis.insights],
                    "connections": analysis.connections,
                    "quality_score": analysis.quality_score
                })

            # Phase 3: Enhanced Connection Discovery
            self.logger.info("Discovering enhanced connections")
            connections = self._discover_enhanced_connections(context)
            results["connections"] = connections

            # Phase 4: Synthesis Generation
            self.logger.info("Generating synthesis")
            synthesis = self._generate_synthesis(context)
            results["synthesis"] = synthesis

            # Add context summary
            results["context_summary"] = context.get_context_summary()

            return results

        except Exception as e:
            self.logger.error(f"Error in progressive analysis: {e}")
            return self._create_error_result(main_topic, str(e))

    def _get_system_prompt(self, level: ContextLevel) -> str:
        """Get appropriate system prompt for context level"""
        prompts = {
            ContextLevel.ROOT: "You are an expert analyst establishing the foundation for comprehensive topic analysis. Focus on creating a framework that will guide subsequent sub-topic analyses.",
            ContextLevel.BRANCH: "You are a research analyst performing context-aware sub-topic analysis. Build upon the main topic framework and previous analyses to provide coherent insights.",
            ContextLevel.CROSS_REF: "You are a systems thinker identifying meaningful connections between topics. Focus on relationships that enhance overall understanding."
        }
        return prompts.get(level, "You are an expert analyst providing comprehensive insights.")

    def _identify_immediate_connections(
        self,
        topic: str,
        context: AnalysisContext,
        analysis: str
    ) -> List[str]:
        """Identify immediate connections from analysis text"""
        connections = []

        # Check for mentions of other topics
        for prev_analysis in context.previous_analyses:
            if prev_analysis.topic.lower() in analysis.lower():
                connections.append(prev_analysis.topic)

        # Check for mentions of main topic
        if context.main_topic.lower() in analysis.lower() and topic != context.main_topic:
            connections.append(context.main_topic)

        return list(set(connections))  # Remove duplicates

    def _calculate_quality_score(
        self,
        analysis: str,
        insights: List[AnalysisInsight]
    ) -> float:
        """Calculate quality score for analysis"""
        if not analysis:
            return 0.0

        # Basic quality metrics
        length_score = min(len(analysis) / 1000, 1.0)  # Normalize to 1000 chars
        insight_score = min(len(insights) / 5, 1.0)    # Normalize to 5 insights

        # Structure score (presence of sections, lists, etc.)
        structure_indicators = ['\n1.', '\n2.', '\n-', '\n*', '##', '**']
        structure_score = sum(1 for indicator in structure_indicators
                            if indicator in analysis) / len(structure_indicators)

        # Average insight confidence
        confidence_score = (
            sum(i.confidence for i in insights) / len(insights)
            if insights else 0.5
        )

        # Weighted average
        return (
            length_score * 0.2 +
            insight_score * 0.3 +
            structure_score * 0.2 +
            confidence_score * 0.3
        )

    def _extract_metadata(self, analysis: str, level: ContextLevel) -> Dict[str, Any]:
        """Extract metadata from analysis"""
        metadata = {
            "word_count": len(analysis.split()),
            "char_count": len(analysis),
            "level": level.value
        }

        # Extract framework for root level analysis
        if level == ContextLevel.ROOT:
            framework = self._extract_framework(analysis)
            metadata["framework"] = framework if framework is not None else {}
        else:
            # Always include framework key for consistency
            metadata["framework"] = {}

        return metadata

    def _extract_framework(self, analysis: str) -> Dict[str, Any]:
        """Extract analysis framework from root topic analysis"""
        framework = {}

        # Look for numbered sections or bullet points
        sections = re.findall(r'\d+\.\s*([^\n]+)', analysis)
        if sections:
            framework["dimensions"] = sections[:5]  # Take first 5

        # Look for key concepts
        key_concepts = re.findall(r'\*\*([^*]+)\*\*', analysis)
        if key_concepts:
            framework["key_concepts"] = key_concepts[:10]  # Take first 10

        return framework

    def _discover_enhanced_connections(self, context: AnalysisContext) -> List[Dict[str, Any]]:
        """Discover enhanced connections between topics"""
        connections = []

        analyses = context.previous_analyses
        if len(analyses) < 2:
            return connections

        # Compare each pair of analyses
        for i in range(len(analyses)):
            for j in range(i + 1, len(analyses)):
                analysis1 = analyses[i]
                analysis2 = analyses[j]

                # Skip if same topic
                if analysis1.topic == analysis2.topic:
                    continue

                # Calculate connection score (simplified)
                score = self._calculate_connection_score(analysis1, analysis2, context)

                if score > 0.3:  # Threshold for meaningful connection
                    connections.append({
                        "topic1": analysis1.topic,
                        "topic2": analysis2.topic,
                        "score": score,
                        "type": self._determine_connection_type(analysis1, analysis2),
                        "description": self._generate_connection_description(analysis1, analysis2)
                    })

        # Sort by score
        connections.sort(key=lambda x: x["score"], reverse=True)
        return connections[:10]  # Return top 10

    def _calculate_connection_score(
        self,
        analysis1: AnalysisResult,
        analysis2: AnalysisResult,
        context: AnalysisContext
    ) -> float:
        """Calculate connection score between two analyses"""

        # Semantic similarity (simplified)
        text1_words = set(analysis1.analysis.lower().split())
        text2_words = set(analysis2.analysis.lower().split())

        if not text1_words or not text2_words:
            return 0.0

        intersection = text1_words.intersection(text2_words)
        union = text1_words.union(text2_words)
        semantic_score = len(intersection) / len(union) if union else 0.0

        # Connection mentions
        mention_score = 0.0
        if analysis2.topic.lower() in analysis1.analysis.lower():
            mention_score += 0.3
        if analysis1.topic.lower() in analysis2.analysis.lower():
            mention_score += 0.3

        # Insight overlap
        insight_score = 0.0
        for insight1 in analysis1.insights:
            for insight2 in analysis2.insights:
                if insight1.type == insight2.type:
                    insight_score += 0.1

        insight_score = min(insight_score, 0.4)  # Cap at 0.4

        return semantic_score * 0.4 + mention_score * 0.4 + insight_score * 0.2

    def _determine_connection_type(
        self,
        analysis1: AnalysisResult,
        analysis2: AnalysisResult
    ) -> str:
        """Determine the type of connection between analyses"""

        # Check for hierarchical relationship
        if analysis1.context_level == ContextLevel.ROOT:
            return "hierarchical"
        if analysis2.context_level == ContextLevel.ROOT:
            return "hierarchical"

        # Check for complementary relationship
        insight_types1 = set(i.type for i in analysis1.insights)
        insight_types2 = set(i.type for i in analysis2.insights)

        if insight_types1.intersection(insight_types2):
            return "complementary"

        # Default to associative
        return "associative"

    def _generate_connection_description(
        self,
        analysis1: AnalysisResult,
        analysis2: AnalysisResult
    ) -> str:
        """Generate a description of the connection"""

        # Find common themes
        words1 = set(analysis1.analysis.lower().split())
        words2 = set(analysis2.analysis.lower().split())
        common_words = words1.intersection(words2)

        # Filter out common words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'}
        meaningful_words = [w for w in common_words if w not in stop_words and len(w) > 3]

        if meaningful_words:
            return f"Connected through: {', '.join(meaningful_words[:3])}"
        else:
            return f"Thematic connection between {analysis1.topic} and {analysis2.topic}"

    def _generate_synthesis(self, context: AnalysisContext) -> str:
        """Generate synthesis of all analyses"""

        if not context.previous_analyses:
            return "No analyses available for synthesis."

        # Build synthesis prompt
        prompt = f"""Based on the comprehensive analysis of "{context.main_topic}" and its sub-topics, please provide a synthesis that:

1. Integrates key insights from all analyses
2. Identifies overarching themes and patterns
3. Highlights the most important connections
4. Provides actionable conclusions

Main Topic: {context.main_topic}

Analyses Summary:
"""

        for analysis in context.previous_analyses:
            prompt += f"\n{analysis.topic}: {analysis.analysis[:200]}...\n"

        prompt += "\nKey Insights:\n"
        for insight in context.running_insights[:10]:
            prompt += f"- {insight.content}\n"

        prompt += "\nPlease provide a comprehensive synthesis that ties everything together."

        return self.generate(
            prompt=prompt,
            system_prompt="You are an expert analyst creating a comprehensive synthesis. Focus on integration, patterns, and actionable insights."
        ) or "Synthesis generation failed."

    def _insight_to_dict(self, insight: AnalysisInsight) -> Dict[str, Any]:
        """Convert insight to dictionary"""
        return {
            "type": insight.type,
            "content": insight.content,
            "confidence": insight.confidence,
            "connections": insight.connections,
            "metadata": insight.metadata
        }

    def _create_empty_result(self, topic: str, level: ContextLevel) -> AnalysisResult:
        """Create empty result for failed analysis"""
        return AnalysisResult(
            topic=topic,
            analysis="Analysis failed to generate.",
            insights=[],
            context_level=level,
            connections=[],
            quality_score=0.0
        )

    def _create_error_result(self, topic: str, error: str) -> Dict[str, Any]:
        """Create error result"""
        return {
            "type": "error",
            "topic": topic,
            "error": error,
            "timestamp": datetime.now().isoformat()
        }